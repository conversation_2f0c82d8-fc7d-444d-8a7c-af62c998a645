"""
综合查询工具 - 语法验证 + 语义验证 + 执行

将原本分散的三个步骤（生成、验证、执行）合并为一个原子操作，
通过双重验证机制（语法+语义）提升查询准确率。
"""

import json
import logging
import os
from typing import Dict, Any, List, Tuple, Optional
from langchain_core.tools import tool
from langchain_google_genai import ChatGoogleGenerativeAI

from .query_validator import validate_flight_query_complete, ValidationResult
from .query_parser import parse_flight_query, QueryParseError, DEFAULT_MODEL_FIELDS

# 配置日志
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.DEBUG)

@tool
def comprehensive_flight_query(query_schema: str, user_question: str) -> str:
    """
    完整的航班查询工具：语法验证 → 语义验证 → 执行
    
    Args:
        query_schema: JSON格式的查询Schema
        user_question: 用户的原始问题
        
    Returns:
        str: JSON格式的查询结果或详细错误信息
    """
    try:
        logger.info(f"开始处理查询: {user_question}")

        # Step 1: 本地语法验证（快速）
        syntax_result = _validate_syntax(query_schema)
        if not syntax_result["valid"]:
            logger.warning(f"语法验证失败: {syntax_result['error']}")
            return _format_error_response(
                "syntax_validation", 
                syntax_result["error"],
                suggestions=syntax_result.get("suggestions", [])
            )

        generated_sql = syntax_result["sql"]
        logger.info(f"生成SQL: {generated_sql}")

        # Step 2: LLM语义验证（可选但推荐）
        semantic_result = _validate_semantics(query_schema, user_question, generated_sql)
        if not semantic_result["valid"]:
            logger.warning(f"语义验证失败: {semantic_result['issues']}")
            return _format_error_response(
                "semantic_validation", 
                semantic_result["issues"],
                suggestions=semantic_result.get("suggestions", []),
                generated_sql=generated_sql
            )

        # Step 3: 执行查询
        execution_result = _execute_query(query_schema)
        logger.info("查询执行成功")
        return execution_result

    except Exception as e:
        logger.error(f"查询处理异常: {str(e)}")
        return _format_error_response("execution", f"执行失败: {str(e)}")


def _validate_syntax(query_schema: str) -> Dict[str, Any]:
    """
    本地语法验证 - 使用统一验证器
    
    验证内容:
    1. JSON格式正确性  
    2. Schema结构验证
    3. 字段存在性验证
    4. 业务规则验证
    5. SQL生成可行性验证
    """
    try:
        # JSON格式验证
        schema_dict = json.loads(query_schema)

        # 使用统一验证器进行完整验证
        validation_result = validate_flight_query_complete(schema_dict)
        
        if not validation_result.valid:
            return {
                "valid": False,
                "error": "; ".join(validation_result.errors),
                "suggestions": ["检查Schema格式和字段定义"] + validation_result.warnings,
                "validation_details": validation_result.validation_details
            }

        # SQL生成测试
        try:
            queryset = parse_flight_query(schema_dict)
            
            # 处理不同类型的查询结果
            if isinstance(queryset, dict):
                # 全局聚合结果
                sql = f"Global aggregation query: {json.dumps(schema_dict, ensure_ascii=False)}"
            elif isinstance(queryset, list):
                # 分组聚合结果
                sql = f"Grouped aggregation query: {json.dumps(schema_dict, ensure_ascii=False)}"
            elif hasattr(queryset, 'query'):
                # 普通QuerySet
                sql = str(queryset.query)
            else:
                # 其他类型
                sql = f"Query executed successfully: {type(queryset).__name__}"
            
            return {
                "valid": True, 
                "sql": sql,
                "validation_details": validation_result.validation_details,
                "query_complexity": validation_result.query_complexity
            }
        except Exception as e:
            return {
                "valid": False,
                "error": f"SQL生成失败: {str(e)}",
                "suggestions": ["检查查询条件的字段名和操作符是否正确"]
            }

    except json.JSONDecodeError as e:
        return {
            "valid": False,
            "error": f"JSON格式错误: {str(e)}",
            "suggestions": ["检查JSON语法，确保所有括号和引号匹配"]
        }




def _validate_semantics(query_schema: str, user_question: str, 
                       generated_sql: str) -> Dict[str, Any]:
    """
    LLM语义验证 - 简化版本
    
    使用LLM分析SQL是否能正确回答用户问题
    """
    validation_prompt = f"""
你是一个SQL查询语义验证专家。请分析生成的SQL查询是否能准确回答用户的问题。

用户问题: {user_question}
查询Schema: {query_schema}
生成的SQL: {generated_sql}

请从以下方面快速评估：
1. 查询是否能回答用户问题
2. 字段选择是否合理
3. 过滤条件是否准确
4. 是否有明显的逻辑错误

返回JSON格式：
{{
    "valid": true/false,
    "confidence": 0.0-1.0,
    "issues": ["问题1", "问题2"],
    "suggestions": ["建议1", "建议2"]
}}

只返回JSON，不要其他内容。
"""

    try:
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.5-flash",
            temperature=0,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        response = llm.invoke(validation_prompt)
        
        # 尝试解析JSON响应
        content = response.content.strip()
        # 如果响应包含```json标记，提取JSON内容
        if '```json' in content:
            content = content.split('```json')[1].split('```')[0].strip()
        elif '```' in content:
            content = content.split('```')[1].split('```')[0].strip()
            
        result = json.loads(content)
        
        # 验证返回格式
        if "valid" not in result:
            result["valid"] = True  # 默认通过
            
        return result

    except Exception as e:
        logger.warning(f"LLM语义验证失败: {str(e)}, 默认通过")
        # 如果LLM验证失败，默认通过以避免阻塞
        return {
            "valid": True,
            "issues": [],
            "suggestions": [],
            "note": f"语义验证跳过: {str(e)}"
        }


def _execute_query(query_schema: str) -> str:
    """执行已验证的查询"""
    try:
        schema_dict = json.loads(query_schema)
        queryset = parse_flight_query(schema_dict)

        # 处理不同类型的查询结果
        if isinstance(queryset, dict):
            # 全局聚合
            result = {
                "success": True,
                "query_type": "global_aggregation",
                "result": queryset,
                "count": 1,
                "sql": str(schema_dict)
            }
        elif isinstance(queryset, list):
            # 分组聚合
            result = {
                "success": True,
                "query_type": "grouped_aggregation",
                "result": queryset,
                "count": len(queryset),
                "sql": str(schema_dict)
            }
        else:
            # 普通查询
            # 检查是否指定了特定字段
            fields = schema_dict.get("fields", [])
            if not fields:
                # 如果没有指定字段，使用模型的默认字段
                model_name = schema_dict.get("model", "")
                fields = DEFAULT_MODEL_FIELDS.get(model_name, [])

            if fields:
                # 如果有字段（指定的或默认的），只返回这些字段
                results = list(queryset.values(*fields))
                selected_fields = fields
            else:
                # 如果没有默认字段配置，返回所有字段（向后兼容）
                results = list(queryset.values())
                selected_fields = "all"

            result = {
                "success": True,
                "query_type": "normal",
                "result": results,
                "count": len(results),
                "sql": str(queryset.query) if hasattr(queryset, 'query') else str(schema_dict),
                "selected_fields": selected_fields
            }

        return json.dumps(result, ensure_ascii=False, indent=2, default=str)

    except Exception as e:
        raise QueryParseError(f"查询执行失败: {str(e)}")


def _format_error_response(stage: str, error: str, 
                          suggestions: Optional[List[str]] = None, 
                          generated_sql: Optional[str] = None) -> str:
    """格式化错误响应"""
    response = {
        "success": False,
        "stage": stage,
        "error": error,
        "suggestions": suggestions or []
    }

    if generated_sql:
        response["generated_sql"] = generated_sql

    return json.dumps(response, ensure_ascii=False, indent=2)

