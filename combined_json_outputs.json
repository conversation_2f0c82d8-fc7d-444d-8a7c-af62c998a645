{"timestamp": "2025-08-22T04:15:06.024228", "description": "comprehensive_flight_query 调用过程中的 introspect_model 和 sample_data 输出", "introspect_model": {"timestamp": "2025-08-22T04:15:05.385235", "function": "introspect_model", "description": "Django模型结构信息", "models": {"aviation_airports": {"model_name": "aviation_airports", "verbose_name": "全球机场表", "fields": {"from_aviation_airports_from": {"type": "ManyToOneRel", "related_model": "aviation_flight", "related_name": "from_aviation_airports_from", "field_name": "from_airport", "multiple": true, "is_reverse": true}, "aviation_airports_to": {"type": "ManyToOneRel", "related_model": "aviation_flight", "related_name": "aviation_airports_to", "field_name": "to_airport", "multiple": true, "is_reverse": true}, "icao_code": {"type": "CharField", "verbose_name": "机场ICAO编号", "help_text": "国际民航组织机场代码，全球唯一标识符"}, "iata_code": {"type": "CharField", "verbose_name": "机场IATA编号", "help_text": "国际航空运输协会机场代码，通常用于航班显示"}, "country": {"type": "CharField", "verbose_name": "国家", "help_text": "机场所在国家名称"}, "province": {"type": "CharField", "verbose_name": "省份"}, "province_en": {"type": "CharField", "verbose_name": "省份英文"}, "prefecture": {"type": "CharField", "verbose_name": "城市"}, "prefecture_en": {"type": "CharField", "verbose_name": "城市英文"}, "name": {"type": "CharField", "verbose_name": "机场名称"}, "name_en": {"type": "CharField", "verbose_name": "机场英文"}, "timezone_name": {"type": "CharField", "verbose_name": "机场时区名称"}, "timezone_short": {"type": "CharField", "verbose_name": "机场时区缩写"}, "timezone": {"type": "IntegerField", "verbose_name": "机场时区"}, "airport_type": {"type": "CharField", "verbose_name": "机场类型"}, "elevation": {"type": "IntegerField", "verbose_name": "海拔", "help_text": "机场的海拔高度，以英尺为单位"}, "country_code": {"type": "CharField", "verbose_name": "国家代码"}, "geom_point": {"type": "GeometryField", "verbose_name": "机场位置点", "help_text": "机场的地理坐标位置，使用WGS84坐标系", "srid": 4326, "dim": 2, "geography": false}, "to_domestic_airports": {"type": "ArrayField", "verbose_name": "飞往国内机场", "help_text": "从该机场出发的国内航班目的地机场列表", "base_field": "TextField", "base_field_max_length": null}, "from_domestic_airports": {"type": "ArrayField", "verbose_name": "飞来国内机场", "help_text": "飞往该机场的国内航班起始机场列表", "base_field": "TextField", "base_field_max_length": null}, "to_international_airports": {"type": "ArrayField", "verbose_name": "飞往国际机场", "help_text": "从该机场出发的国际航班目的地机场列表", "base_field": "TextField", "base_field_max_length": null}, "from_international_airports": {"type": "ArrayField", "verbose_name": "飞来国际机场", "help_text": "飞往该机场的国际航班起始机场列表", "base_field": "TextField", "base_field_max_length": null}, "latest_flight_time": {"type": "DateTimeField", "verbose_name": "最新飞行时间"}, "earliest_flight_time": {"type": "DateTimeField", "verbose_name": "最早飞行时间"}, "admin": {"type": "ForeignKey", "verbose_name": "行政ID", "related_model": "world_admin", "related_field": "base_id"}, "military": {"type": "BooleanField", "verbose_name": "军事基地"}}, "primary_key": "icao_code", "indexes": ["admin", "country", "geom_point", "military"]}, "aviation_aircraft": {"model_name": "aviation_aircraft", "verbose_name": "全球飞机表", "fields": {"aviation_flight": {"type": "ManyToOneRel", "related_model": "aviation_flight", "related_name": "aviation_flight_set", "field_name": "aircraft", "multiple": true, "is_reverse": true}, "registration": {"type": "CharField", "verbose_name": "飞行器注册号"}, "model_name": {"type": "CharField", "verbose_name": "机型名称"}, "model_s": {"type": "CharField", "verbose_name": "MODE-S"}, "serial_number": {"type": "CharField", "verbose_name": "序列号"}, "age": {"type": "IntegerField", "verbose_name": "机龄"}, "born": {"type": "DateTimeField", "verbose_name": "服役时间"}, "track": {"type": "BooleanField", "verbose_name": "跟踪轨迹"}, "engines": {"type": "CharField", "verbose_name": "引擎"}, "layout": {"type": "CharField", "verbose_name": "仓位布局"}, "total_passenger": {"type": "IntegerField", "verbose_name": "载客量"}, "spotter_source": {"type": "URLField", "verbose_name": "spotter链接"}, "as_cargo": {"type": "BooleanField", "verbose_name": "货机"}, "as_cargo_date": {"type": "DateTimeField", "verbose_name": "疑似货机时间"}, "military": {"type": "BooleanField", "verbose_name": "军机"}, "manufacturer": {"type": "CharField", "verbose_name": "制造商"}, "airline_name": {"type": "CharField", "verbose_name": "航空公司名称"}}, "primary_key": "registration", "indexes": ["airline_name", "as_cargo", "manufacturer", "military", "model_name"]}, "aviation_flight": {"model_name": "aviation_flight", "verbose_name": "全球航班表", "fields": {"flight_id": {"type": "PositiveBigIntegerField", "verbose_name": "Radarbox飞行ID", "help_text": "Radarbox系统中的唯一航班标识符"}, "aircraft": {"type": "ForeignKey", "verbose_name": "航空器", "help_text": "执行该航班的飞机信息", "related_model": "aviation_aircraft", "related_field": "registration"}, "flight_number_icao": {"type": "CharField", "verbose_name": "航班编号（ICAO）", "help_text": "国际民航组织标准的航班号"}, "flight_number_iata": {"type": "CharField", "verbose_name": "航班编号（IATA）", "help_text": "国际航空运输协会标准的航班号，通常用于乘客显示"}, "code_shares": {"type": "ArrayField", "verbose_name": "共享航班号", "base_field": "TextField", "base_field_max_length": null}, "airline_iata": {"type": "CharField", "verbose_name": "航空公司代码（IATA）"}, "airline_icao": {"type": "CharField", "verbose_name": "航空公司代码（ICAO）"}, "airline_name": {"type": "CharField", "verbose_name": "航空公司名称"}, "from_airport": {"type": "ForeignKey", "verbose_name": "出发机场", "related_model": "aviation_airports", "related_field": "icao_code"}, "to_airport": {"type": "ForeignKey", "verbose_name": "到达机场", "related_model": "aviation_airports", "related_field": "icao_code"}, "status": {"type": "CharField", "verbose_name": "航班状态"}, "departure_scheduled_time": {"type": "DateTimeField", "verbose_name": "计划起飞时间"}, "departure_actual_time": {"type": "DateTimeField", "verbose_name": "起飞时间"}, "arrival_scheduled_time": {"type": "DateTimeField", "verbose_name": "计划到达时间"}, "arrival_actual_time": {"type": "DateTimeField", "verbose_name": "到达时间"}, "arrdeld": {"type": "CharField", "verbose_name": "到达ID"}, "tkosrc": {"type": "CharField", "verbose_name": "tkosrc"}, "deptaxi": {"type": "CharField", "verbose_name": "deptaxi"}, "depgate": {"type": "CharField", "verbose_name": "登机口"}, "arrgate": {"type": "CharField", "verbose_name": "到达口"}, "as_international": {"type": "BooleanField", "verbose_name": "是否为国际航班"}}, "primary_key": "flight_id", "indexes": ["aircraft", "airline_name", "as_international", "departure_scheduled_time", "flight_id", "from_airport", "status", "to_airport"]}}}, "sample_data": {"timestamp": "2025-08-22T04:15:06.022397", "function": "get_sample_data", "description": "Django模型样例数据", "parameters": {"limit": 3, "random": true, "exclude_sys_fields": true}, "models": {"aviation_airports": {"model_name": "aviation_airports", "total_count": 47698, "sample_count": 3, "sample_data": [{"icao_code": "CGH2", "iata_code": "", "country": "Canada", "province": "CA-NL", "province_en": "CA-NL", "prefecture": "甘德", "prefecture_en": "Gander", "name": "<PERSON><PERSON> (<PERSON> Memorial Regional Health Centre) Heliport", "name_en": "<PERSON><PERSON> (<PERSON> Memorial Regional Health Centre) Heliport", "timezone_name": "Newfoundland Daylight Time", "timezone_short": "NDT", "timezone": -2, "airport_type": "heliport", "elevation": 432, "country_code": "CA", "geom_point": {"type": "geometry", "note": "几何数据已省略"}, "to_domestic_airports": null, "from_domestic_airports": null, "to_international_airports": null, "from_international_airports": null, "latest_flight_time": null, "earliest_flight_time": null, "admin": "9707ba1c-a066-57d3-9090-cd5871005089", "military": false}, {"icao_code": "7OR2", "iata_code": "", "country": "United States", "province": null, "province_en": null, "prefecture": "<PERSON><PERSON><PERSON>", "prefecture_en": "<PERSON><PERSON><PERSON>", "name": "Basl Hill Farms Airport", "name_en": "Basl Hill Farms Airport", "timezone_name": "Pacific Daylight Time", "timezone_short": "PDT", "timezone": -7, "airport_type": "mini_airport", "elevation": 1160, "country_code": "US", "geom_point": {"type": "geometry", "note": "几何数据已省略"}, "to_domestic_airports": null, "from_domestic_airports": null, "to_international_airports": null, "from_international_airports": null, "latest_flight_time": null, "earliest_flight_time": null, "admin": "deea4376-2af1-598c-b38d-e5581f4a5c7a", "military": false}, {"icao_code": "HTSH", "iata_code": "", "country": "Tanzania", "province": "Iringa", "province_en": "Iringa", "prefecture": "<PERSON><PERSON><PERSON>", "prefecture_en": "<PERSON><PERSON><PERSON>", "name": "Mafinga Airport", "name_en": "Mafinga Airport", "timezone_name": "East Africa Time", "timezone_short": "EAT", "timezone": 3, "airport_type": "mini_airport", "elevation": 6300, "country_code": "TZ", "geom_point": {"type": "geometry", "note": "几何数据已省略"}, "to_domestic_airports": null, "from_domestic_airports": null, "to_international_airports": null, "from_international_airports": null, "latest_flight_time": null, "earliest_flight_time": null, "admin": "8036427f-281d-5112-ae09-94abdd459e43", "military": false}], "fields_info": {"icao_code": "CharField", "iata_code": "CharField", "country": "CharField", "province": "CharField", "province_en": "CharField", "prefecture": "CharField", "prefecture_en": "CharField", "name": "CharField", "name_en": "CharField", "timezone_name": "CharField", "timezone_short": "CharField", "timezone": "IntegerField", "airport_type": "CharField", "elevation": "IntegerField", "country_code": "CharField", "geom_point": "GeometryField (SRID: 4326)", "to_domestic_airports": "ArrayField[TextField]", "from_domestic_airports": "ArrayField[TextField]", "to_international_airports": "ArrayField[TextField]", "from_international_airports": "ArrayField[TextField]", "latest_flight_time": "DateTimeField", "earliest_flight_time": "DateTimeField", "admin": "ForeignKey -> world_admin", "military": "BooleanField"}, "sample_type": "random"}, "aviation_aircraft": {"model_name": "aviation_aircraft", "total_count": 437991, "sample_count": 3, "sample_data": [{"registration": "N29S", "model_name": "Bell 206B JetRanger II", "model_s": "A2F4B1", "serial_number": "4378", "age": null, "born": null, "track": false, "engines": null, "layout": null, "total_passenger": null, "spotter_source": null, "as_cargo": false, "as_cargo_date": null, "military": false, "manufacturer": "BELL", "airline_name": "Private"}, {"registration": "EZ-A778", "model_name": "Boeing 777-22KLR", "model_s": "60183A", "serial_number": "42296/1181", "age": 2620, "born": "2014-02-18T16:00:00+00:00", "track": false, "engines": null, "layout": null, "total_passenger": null, "spotter_source": null, "as_cargo": false, "as_cargo_date": null, "military": false, "manufacturer": "BOEING", "airline_name": "Turkmenistan Airlines"}, {"registration": "N7275T", "model_name": null, "model_s": "A9C0A6", "serial_number": "46875", "age": null, "born": null, "track": false, "engines": null, "layout": null, "total_passenger": null, "spotter_source": null, "as_cargo": false, "as_cargo_date": null, "military": false, "manufacturer": null, "airline_name": "Private"}], "fields_info": {"registration": "CharField", "model_name": "CharField", "model_s": "CharField", "serial_number": "CharField", "age": "IntegerField", "born": "DateTimeField", "track": "BooleanField", "engines": "CharField", "layout": "CharField", "total_passenger": "IntegerField", "spotter_source": "URLField", "as_cargo": "BooleanField", "as_cargo_date": "DateTimeField", "military": "BooleanField", "manufacturer": "CharField", "airline_name": "CharField"}, "sample_type": "random"}, "aviation_flight": {"model_name": "aviation_flight", "total_count": 9999999, "sample_count": 3, "sample_data": [{"flight_id": 2494533077, "aircraft": "TS-IMB", "flight_number_icao": "TAR756", "flight_number_iata": "TU756", "code_shares": null, "airline_iata": "TU", "airline_icao": "TAR", "airline_name": "<PERSON><PERSON>", "from_airport": "LIMC", "to_airport": "LIRF", "status": "landed", "departure_scheduled_time": "2025-07-10T09:20:00+00:00", "departure_actual_time": "2025-07-10T09:20:00+00:00", "arrival_scheduled_time": "2025-07-10T10:30:00+00:00", "arrival_actual_time": "2025-07-10T11:55:55+00:00", "arrdeld": "ARVS", "tkosrc": "guessed", "deptaxi": null, "depgate": null, "arrgate": null, "as_international": false}, {"flight_id": 2496034337, "aircraft": "C-GCXD", "flight_number_icao": null, "flight_number_iata": "CGCXD", "code_shares": null, "airline_iata": null, "airline_icao": null, "airline_name": null, "from_airport": "CYVT", "to_airport": "CYQR", "status": "landed", "departure_scheduled_time": "2025-07-11T23:03:35+00:00", "departure_actual_time": "2025-07-11T23:03:35+00:00", "arrival_scheduled_time": "2025-07-11T23:55:34+00:00", "arrival_actual_time": "2025-07-11T23:55:34+00:00", "arrdeld": null, "tkosrc": "guessed", "deptaxi": null, "depgate": null, "arrgate": null, "as_international": false}, {"flight_id": 2494304744, "aircraft": "B-658K", "flight_number_icao": "CSN5741", "flight_number_iata": "CZ5741", "code_shares": null, "airline_iata": "CZ", "airline_icao": "CSN", "airline_name": "China Southern Airlines", "from_airport": "ZGOW", "to_airport": "ZSPD", "status": "landed", "departure_scheduled_time": "2025-07-10T05:10:00+00:00", "departure_actual_time": "2025-07-10T05:10:00+00:00", "arrival_scheduled_time": "2025-07-10T07:20:00+00:00", "arrival_actual_time": "2025-07-10T08:27:43+00:00", "arrdeld": "EON", "tkosrc": "guessed", "deptaxi": null, "depgate": "115", "arrgate": null, "as_international": false}], "fields_info": {"flight_id": "PositiveBigIntegerField", "aircraft": "ForeignKey -> aviation_aircraft", "flight_number_icao": "CharField", "flight_number_iata": "CharField", "code_shares": "ArrayField[TextField]", "airline_iata": "CharField", "airline_icao": "CharField", "airline_name": "CharField", "from_airport": "ForeignKey -> aviation_airports", "to_airport": "ForeignKey -> aviation_airports", "status": "CharField", "departure_scheduled_time": "DateTimeField", "departure_actual_time": "DateTimeField", "arrival_scheduled_time": "DateTimeField", "arrival_actual_time": "DateTimeField", "arrdeld": "CharField", "tkosrc": "CharField", "deptaxi": "CharField", "depgate": "CharField", "arrgate": "CharField", "as_international": "BooleanField"}, "sample_type": "random"}}}}