#!/usr/bin/env python3
"""
Graph端到端任务完成能力测试

专门测试graph系统的端到端任务完成能力，包括：
1. 多轮对话支持
2. 详细行为追踪
3. 可视化输出
4. 精简设计，专注核心功能

测试覆盖不同类型的用户查询场景，帮助理解和调试graph系统的工作流程。
"""

import time
import json
from typing import List, Dict, Any
from flights_workflow.graph import graph, AgentState
from langchain_core.messages import HumanMessage, BaseMessage


class ConversationTracker:
    """对话追踪器 - 记录和可视化对话过程"""
    
    def __init__(self):
        self.conversations = []
        self.current_conversation = None
    
    def start_conversation(self, conversation_id: str, description: str):
        """开始新的对话"""
        self.current_conversation = {
            "id": conversation_id,
            "description": description,
            "start_time": time.time(),
            "rounds": [],
            "total_messages": 0,
            "tools_used": [],
            "success": False
        }
    
    def add_round(self, user_input: str, messages: List[BaseMessage]):
        """添加一轮对话"""
        if not self.current_conversation:
            return
        
        round_data = {
            "round_number": len(self.current_conversation["rounds"]) + 1,
            "user_input": user_input,
            "timestamp": time.time(),
            "messages": [],
            "tools_called": [],
            "final_response": ""
        }
        
        # 分析消息
        for msg in messages:
            msg_data = {
                "type": type(msg).__name__,
                "content": getattr(msg, 'content', ''),
                "tool_calls": []
            }
            
            # 处理工具调用
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                for tool_call in msg.tool_calls:
                    tool_data = {
                        "name": tool_call.get('name', ''),
                        "args": tool_call.get('args', {}),
                        "id": tool_call.get('id', '')
                    }
                    msg_data["tool_calls"].append(tool_data)
                    round_data["tools_called"].append(tool_data["name"])
                    
                    # 记录到总工具使用列表
                    if tool_data["name"] not in self.current_conversation["tools_used"]:
                        self.current_conversation["tools_used"].append(tool_data["name"])
            
            round_data["messages"].append(msg_data)
        
        # 获取最终响应
        if messages:
            last_msg = messages[-1]
            if hasattr(last_msg, 'content') and last_msg.content:
                round_data["final_response"] = last_msg.content
        
        self.current_conversation["rounds"].append(round_data)
        self.current_conversation["total_messages"] += len(messages)
    
    def end_conversation(self, success: bool = True):
        """结束当前对话"""
        if not self.current_conversation:
            return
        
        self.current_conversation["success"] = success
        self.current_conversation["end_time"] = time.time()
        self.current_conversation["duration"] = (
            self.current_conversation["end_time"] - self.current_conversation["start_time"]
        )
        
        self.conversations.append(self.current_conversation)
        self.current_conversation = None
    
    def print_conversation_summary(self):
        """打印对话摘要"""
        if not self.conversations:
            return
        
        conv = self.conversations[-1]
        print("\n" + "=" * 80)
        print(f"📊 对话摘要: {conv['description']}")
        print("=" * 80)
        print(f"🆔 对话ID: {conv['id']}")
        print(f"⏱️ 总耗时: {conv['duration']:.2f}秒")
        print(f"🔄 对话轮数: {len(conv['rounds'])}")
        print(f"💬 总消息数: {conv['total_messages']}")
        print(f"🔧 使用工具: {', '.join(conv['tools_used']) if conv['tools_used'] else '无'}")
        print(f"✅ 执行状态: {'成功' if conv['success'] else '失败'}")
    
    def print_detailed_flow(self):
        """打印详细的执行流程"""
        if not self.conversations:
            return
        
        conv = self.conversations[-1]
        print("\n" + "=" * 80)
        print(f"🔍 详细执行流程: {conv['description']}")
        print("=" * 80)
        
        for round_data in conv["rounds"]:
            print(f"\n📝 第{round_data['round_number']}轮对话")
            print("-" * 50)
            print(f"👤 用户输入: {round_data['user_input']}")
            
            # 显示推理和工具调用过程
            for i, msg in enumerate(round_data["messages"], 1):
                msg_type = msg["type"]
                content = msg["content"]
                
                if msg_type == "AIMessage":
                    if msg["tool_calls"]:
                        print(f"🤖 AI推理: {content}")
                        for tool_call in msg["tool_calls"]:
                            args_str = json.dumps(tool_call["args"], ensure_ascii=False)
                            print(f"   🔧 调用工具: {tool_call['name']}({args_str})")
                    else:
                        print(f"🤖 AI回答: {content}")
                
                elif msg_type == "ToolMessage":
                    print(f"   📊 工具结果: {content[:200]}{'...' if len(content) > 200 else ''}")
            
            if round_data["final_response"]:
                print(f"💡 最终回答: {round_data['final_response']}")


def run_single_conversation(tracker: ConversationTracker, conversation_id: str, 
                          description: str, user_inputs: List[str], config: Dict[str, Any]):
    """运行单个对话测试"""
    print(f"\n🚀 开始测试: {description}")
    print("=" * 60)
    
    tracker.start_conversation(conversation_id, description)
    
    try:
        # 初始化状态
        messages = []
        
        for i, user_input in enumerate(user_inputs, 1):
            print(f"\n📝 第{i}轮 - 用户: {user_input}")
            
            # 添加用户消息
            messages.append(HumanMessage(content=user_input))
            
            # 构建状态
            state = {"messages": messages}
            
            # 调用graph
            start_time = time.time()
            result = graph.invoke(state, config=config)
            end_time = time.time()
            
            # 更新消息历史
            messages = result["messages"]
            
            # 记录这一轮
            tracker.add_round(user_input, messages)
            
            print(f"⏱️ 本轮耗时: {end_time - start_time:.2f}秒")
            
            # 显示AI的最终回答
            if messages:
                last_msg = messages[-1]
                if hasattr(last_msg, 'content') and last_msg.content:
                    print(f"🤖 AI回答: {last_msg.content}")
        
        tracker.end_conversation(success=True)
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        tracker.end_conversation(success=False)
        return False


def test_flight_query_conversation():
    """测试航班查询对话"""
    tracker = ConversationTracker()
    
    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash",
            "temperature": 0.3,
            "enabled_tools": [
                "generate_flight_query",
                "comprehensive_flight_query",
                "get_current_time"
            ]
        }
    }
    
    user_inputs = [
        "我想查询从北京到上海的航班",
        "我希望是明天的航班",
        "有没有下午的航班？"
    ]
    
    success = run_single_conversation(
        tracker, 
        "flight_query_001", 
        "航班查询多轮对话测试",
        user_inputs,
        config
    )
    
    # 打印结果
    tracker.print_conversation_summary()
    tracker.print_detailed_flow()
    
    return success


def test_time_weather_conversation():
    """测试时间天气查询对话"""
    tracker = ConversationTracker()
    
    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash",
            "temperature": 0.3,
            "enabled_tools": [
                "get_current_time",
                "get_weather"
            ]
        }
    }
    
    user_inputs = [
        "现在几点了？",
        "北京的天气怎么样？",
        "如果现在是下午，推荐我一些适合的户外活动"
    ]
    
    success = run_single_conversation(
        tracker,
        "time_weather_001",
        "时间天气查询对话测试", 
        user_inputs,
        config
    )
    
    # 打印结果
    tracker.print_conversation_summary()
    tracker.print_detailed_flow()
    
    return success


def test_simple_tool_conversation():
    """测试简单工具调用对话"""
    tracker = ConversationTracker()

    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash",
            "temperature": 0.3,
            "enabled_tools": [
                "get_current_time",
                "get_weather"
            ]
        }
    }

    user_inputs = [
        "现在几点了？",
        "上海的天气怎么样？"
    ]

    success = run_single_conversation(
        tracker,
        "simple_tool_001",
        "简单工具调用对话测试",
        user_inputs,
        config
    )

    # 打印结果
    tracker.print_conversation_summary()
    tracker.print_detailed_flow()

    return success


def run_all_e2e_tests():
    """运行所有端到端测试"""
    print("🎯 Graph端到端任务完成能力测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    test_results = []
    
    # 测试1: 航班查询对话
    print("\n🛫 测试1: 航班查询多轮对话")
    result1 = test_flight_query_conversation()
    test_results.append(("航班查询对话", result1))
    
    # 测试2: 时间天气查询对话  
    print("\n🌤️ 测试2: 时间天气查询对话")
    result2 = test_time_weather_conversation()
    test_results.append(("时间天气对话", result2))
    
    # 测试3: 简单工具调用对话
    print("\n🔄 测试3: 简单工具调用对话")
    result3 = test_simple_tool_conversation()
    test_results.append(("简单工具调用对话", result3))
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    success_count = sum(1 for _, success in test_results if success)
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有端到端测试均通过！Graph系统工作正常。")
    else:
        print("⚠️ 部分测试失败，需要检查Graph系统配置。")
    
    return success_count == total_count


def demo_conversation_tracking():
    """演示对话追踪功能"""
    print("🎯 Graph端到端任务完成能力测试 - 演示模式")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 只运行简单的时间天气测试，避免API配额问题
    print("\n🌤️ 演示: 时间天气查询对话")
    result = test_time_weather_conversation()

    print("\n" + "=" * 80)
    print("📋 演示总结")
    print("=" * 80)

    if result:
        print("✅ 演示成功完成！")
        print("💡 测试文件功能特点:")
        print("   - ✅ 多轮对话支持")
        print("   - ✅ 详细行为追踪")
        print("   - ✅ 可视化输出")
        print("   - ✅ 工具调用监控")
        print("   - ✅ 性能统计")
    else:
        print("❌ 演示失败")

    return result


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        success = demo_conversation_tracking()
    else:
        success = run_all_e2e_tests()

    exit(0 if success else 1)
