#!/usr/bin/env python3
"""
捕获 comprehensive_flight_query 调用过程中 introspect_model 和 sample_data 的 JSON 输出
"""

import os
import sys
import json
import django
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置Django设置
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from flights_workflow.tools.introspect_models import introspect_model
from flights_workflow.tools.sample_data import get_sample_data
from flights_workflow.tools.model_context_tool import CustomJSONEncoder


def capture_introspect_model_output(model_names, output_file="introspect_model_output.json"):
    """
    捕获 introspect_model 函数的输出
    
    Args:
        model_names: 模型名称列表
        output_file: 输出文件名
    """
    print(f"正在捕获 introspect_model 输出...")
    
    results = {}
    for model_name in model_names:
        print(f"  处理模型: {model_name}")
        try:
            result = introspect_model(
                model_name=model_name,
                include_relationships=True,
                include_meta=True,
                exclude_sys_fields=True
            )
            results[model_name] = result
            print(f"    ✅ 成功获取 {model_name} 的结构信息")
        except Exception as e:
            print(f"    ❌ 获取 {model_name} 失败: {str(e)}")
            results[model_name] = {"error": str(e)}
    
    # 添加元数据
    output_data = {
        "timestamp": datetime.now().isoformat(),
        "function": "introspect_model",
        "description": "Django模型结构信息",
        "models": results
    }
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    print(f"✅ introspect_model 输出已保存到: {output_file}")
    return output_data


def capture_sample_data_output(model_names, output_file="sample_data_output.json", limit=5):
    """
    捕获 get_sample_data 函数的输出
    
    Args:
        model_names: 模型名称列表
        output_file: 输出文件名
        limit: 每个模型的样例数据条数
    """
    print(f"正在捕获 sample_data 输出...")
    
    results = {}
    for model_name in model_names:
        print(f"  处理模型: {model_name}")
        try:
            result = get_sample_data(
                model_name=model_name,
                limit=limit,
                offset=0,
                random=True,
                exclude_sys_fields=True
            )
            results[model_name] = result
            print(f"    ✅ 成功获取 {model_name} 的样例数据 ({result.get('sample_count', 0)} 条)")
        except Exception as e:
            print(f"    ❌ 获取 {model_name} 失败: {str(e)}")
            results[model_name] = {"error": str(e)}
    
    # 添加元数据
    output_data = {
        "timestamp": datetime.now().isoformat(),
        "function": "get_sample_data",
        "description": "Django模型样例数据",
        "parameters": {
            "limit": limit,
            "random": True,
            "exclude_sys_fields": True
        },
        "models": results
    }
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    print(f"✅ sample_data 输出已保存到: {output_file}")
    return output_data


def main():
    """主函数"""
    print("=" * 60)
    print("捕获 comprehensive_flight_query 调用的 JSON 输出")
    print("=" * 60)
    
    # 常用的模型列表（根据实际使用情况调整）
    model_names = [
        'aviation_airports',
        'aviation_aircraft', 
        'aviation_flight',
    ]
    
    print(f"目标模型: {', '.join(model_names)}")
    print()
    
    try:
        # 捕获 introspect_model 输出
        introspect_output = capture_introspect_model_output(
            model_names, 
            "introspect_model_output.json"
        )
        print()
        
        # 捕获 sample_data 输出
        sample_data_output = capture_sample_data_output(
            model_names, 
            "sample_data_output.json",
            limit=3  # 与 comprehensive_flight_query 中的设置一致
        )
        print()
        
        # 创建合并的输出文件
        combined_output = {
            "timestamp": datetime.now().isoformat(),
            "description": "comprehensive_flight_query 调用过程中的 introspect_model 和 sample_data 输出",
            "introspect_model": introspect_output,
            "sample_data": sample_data_output
        }
        
        with open("combined_json_outputs.json", 'w', encoding='utf-8') as f:
            json.dump(combined_output, f, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
        
        print("✅ 合并输出已保存到: combined_json_outputs.json")
        print()
        print("=" * 60)
        print("捕获完成！生成的文件:")
        print("  - introspect_model_output.json")
        print("  - sample_data_output.json") 
        print("  - combined_json_outputs.json")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 捕获过程中发生错误: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
