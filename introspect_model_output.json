{"timestamp": "2025-08-22T04:15:05.385235", "function": "introspect_model", "description": "Django模型结构信息", "models": {"aviation_airports": {"model_name": "aviation_airports", "verbose_name": "全球机场表", "fields": {"from_aviation_airports_from": {"type": "ManyToOneRel", "related_model": "aviation_flight", "related_name": "from_aviation_airports_from", "field_name": "from_airport", "multiple": true, "is_reverse": true}, "aviation_airports_to": {"type": "ManyToOneRel", "related_model": "aviation_flight", "related_name": "aviation_airports_to", "field_name": "to_airport", "multiple": true, "is_reverse": true}, "icao_code": {"type": "CharField", "verbose_name": "机场ICAO编号", "help_text": "国际民航组织机场代码，全球唯一标识符"}, "iata_code": {"type": "CharField", "verbose_name": "机场IATA编号", "help_text": "国际航空运输协会机场代码，通常用于航班显示"}, "country": {"type": "CharField", "verbose_name": "国家", "help_text": "机场所在国家名称"}, "province": {"type": "CharField", "verbose_name": "省份"}, "province_en": {"type": "CharField", "verbose_name": "省份英文"}, "prefecture": {"type": "CharField", "verbose_name": "城市"}, "prefecture_en": {"type": "CharField", "verbose_name": "城市英文"}, "name": {"type": "CharField", "verbose_name": "机场名称"}, "name_en": {"type": "CharField", "verbose_name": "机场英文"}, "timezone_name": {"type": "CharField", "verbose_name": "机场时区名称"}, "timezone_short": {"type": "CharField", "verbose_name": "机场时区缩写"}, "timezone": {"type": "IntegerField", "verbose_name": "机场时区"}, "airport_type": {"type": "CharField", "verbose_name": "机场类型"}, "elevation": {"type": "IntegerField", "verbose_name": "海拔", "help_text": "机场的海拔高度，以英尺为单位"}, "country_code": {"type": "CharField", "verbose_name": "国家代码"}, "geom_point": {"type": "GeometryField", "verbose_name": "机场位置点", "help_text": "机场的地理坐标位置，使用WGS84坐标系", "srid": 4326, "dim": 2, "geography": false}, "to_domestic_airports": {"type": "ArrayField", "verbose_name": "飞往国内机场", "help_text": "从该机场出发的国内航班目的地机场列表", "base_field": "TextField", "base_field_max_length": null}, "from_domestic_airports": {"type": "ArrayField", "verbose_name": "飞来国内机场", "help_text": "飞往该机场的国内航班起始机场列表", "base_field": "TextField", "base_field_max_length": null}, "to_international_airports": {"type": "ArrayField", "verbose_name": "飞往国际机场", "help_text": "从该机场出发的国际航班目的地机场列表", "base_field": "TextField", "base_field_max_length": null}, "from_international_airports": {"type": "ArrayField", "verbose_name": "飞来国际机场", "help_text": "飞往该机场的国际航班起始机场列表", "base_field": "TextField", "base_field_max_length": null}, "latest_flight_time": {"type": "DateTimeField", "verbose_name": "最新飞行时间"}, "earliest_flight_time": {"type": "DateTimeField", "verbose_name": "最早飞行时间"}, "admin": {"type": "ForeignKey", "verbose_name": "行政ID", "related_model": "world_admin", "related_field": "base_id"}, "military": {"type": "BooleanField", "verbose_name": "军事基地"}}, "primary_key": "icao_code", "indexes": ["admin", "country", "geom_point", "military"]}, "aviation_aircraft": {"model_name": "aviation_aircraft", "verbose_name": "全球飞机表", "fields": {"aviation_flight": {"type": "ManyToOneRel", "related_model": "aviation_flight", "related_name": "aviation_flight_set", "field_name": "aircraft", "multiple": true, "is_reverse": true}, "registration": {"type": "CharField", "verbose_name": "飞行器注册号"}, "model_name": {"type": "CharField", "verbose_name": "机型名称"}, "model_s": {"type": "CharField", "verbose_name": "MODE-S"}, "serial_number": {"type": "CharField", "verbose_name": "序列号"}, "age": {"type": "IntegerField", "verbose_name": "机龄"}, "born": {"type": "DateTimeField", "verbose_name": "服役时间"}, "track": {"type": "BooleanField", "verbose_name": "跟踪轨迹"}, "engines": {"type": "CharField", "verbose_name": "引擎"}, "layout": {"type": "CharField", "verbose_name": "仓位布局"}, "total_passenger": {"type": "IntegerField", "verbose_name": "载客量"}, "spotter_source": {"type": "URLField", "verbose_name": "spotter链接"}, "as_cargo": {"type": "BooleanField", "verbose_name": "货机"}, "as_cargo_date": {"type": "DateTimeField", "verbose_name": "疑似货机时间"}, "military": {"type": "BooleanField", "verbose_name": "军机"}, "manufacturer": {"type": "CharField", "verbose_name": "制造商"}, "airline_name": {"type": "CharField", "verbose_name": "航空公司名称"}}, "primary_key": "registration", "indexes": ["airline_name", "as_cargo", "manufacturer", "military", "model_name"]}, "aviation_flight": {"model_name": "aviation_flight", "verbose_name": "全球航班表", "fields": {"flight_id": {"type": "PositiveBigIntegerField", "verbose_name": "Radarbox飞行ID", "help_text": "Radarbox系统中的唯一航班标识符"}, "aircraft": {"type": "ForeignKey", "verbose_name": "航空器", "help_text": "执行该航班的飞机信息", "related_model": "aviation_aircraft", "related_field": "registration"}, "flight_number_icao": {"type": "CharField", "verbose_name": "航班编号（ICAO）", "help_text": "国际民航组织标准的航班号"}, "flight_number_iata": {"type": "CharField", "verbose_name": "航班编号（IATA）", "help_text": "国际航空运输协会标准的航班号，通常用于乘客显示"}, "code_shares": {"type": "ArrayField", "verbose_name": "共享航班号", "base_field": "TextField", "base_field_max_length": null}, "airline_iata": {"type": "CharField", "verbose_name": "航空公司代码（IATA）"}, "airline_icao": {"type": "CharField", "verbose_name": "航空公司代码（ICAO）"}, "airline_name": {"type": "CharField", "verbose_name": "航空公司名称"}, "from_airport": {"type": "ForeignKey", "verbose_name": "出发机场", "related_model": "aviation_airports", "related_field": "icao_code"}, "to_airport": {"type": "ForeignKey", "verbose_name": "到达机场", "related_model": "aviation_airports", "related_field": "icao_code"}, "status": {"type": "CharField", "verbose_name": "航班状态"}, "departure_scheduled_time": {"type": "DateTimeField", "verbose_name": "计划起飞时间"}, "departure_actual_time": {"type": "DateTimeField", "verbose_name": "起飞时间"}, "arrival_scheduled_time": {"type": "DateTimeField", "verbose_name": "计划到达时间"}, "arrival_actual_time": {"type": "DateTimeField", "verbose_name": "到达时间"}, "arrdeld": {"type": "CharField", "verbose_name": "到达ID"}, "tkosrc": {"type": "CharField", "verbose_name": "tkosrc"}, "deptaxi": {"type": "CharField", "verbose_name": "deptaxi"}, "depgate": {"type": "CharField", "verbose_name": "登机口"}, "arrgate": {"type": "CharField", "verbose_name": "到达口"}, "as_international": {"type": "BooleanField", "verbose_name": "是否为国际航班"}}, "primary_key": "flight_id", "indexes": ["aircraft", "airline_name", "as_international", "departure_scheduled_time", "flight_id", "from_airport", "status", "to_airport"]}}}